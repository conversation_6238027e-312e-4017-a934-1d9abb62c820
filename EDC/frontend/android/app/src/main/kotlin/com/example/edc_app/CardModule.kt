package com.example.edc_app

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import sunmi.paylib.SunmiPayKernel
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.AidlErrorCodeV2
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2
import com.sunmi.pay.hardware.aidlv2.AidlConstantsV2
import java.util.*
import com.example.edc_app.EdcApplication

/**
 * 卡模块实现类
 * 负责调用商米SDK实现卡模块功能
 */
class CardModule(private val context: Context) {
    companion object {
        private const val TAG = "CardModule"
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    
    private var checkCardCallback: CheckCardCallbackV2? = null
    private var resultCallback: MethodChannel.Result? = null
    private var channel: MethodChannel? = null
    
    /**
     * 设置 MethodChannel (如果需要回调 Flutter)
     */
    fun setChannel(channel: MethodChannel) {
        this.channel = channel
        Log.d(TAG, "MethodChannel set for CardModule")
    }
    
    /**
     * 开始检测NFC卡
     */
    fun startCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "startCheckNFCCard called.")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to start NFC check. SDK might not be connected.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接或CardModule未正确初始化", null)
            return
        }

        try {
            Log.i(TAG, "Attempting to start NFC card check using $currentReadCardOptV2")
            resultCallback = result
            
            checkCardCallback = object : CheckCardCallbackV2.Stub() {
                @Throws(RemoteException::class)
                override fun findMagCard(info: Bundle) {
                    Log.d(TAG, "findMagCard: $info")
                }
                
                @Throws(RemoteException::class)
                override fun findICCardEx(info: Bundle) {
                    Log.d(TAG, "findICCardEx: $info")
                }
                
                @Throws(RemoteException::class)
                override fun findRFCardEx(info: Bundle) {
                    Log.d(TAG, "findRFCardEx from Binder thread: $info")
                    val cardType = info.getInt("cardType")
                    val uuid = info.getString("uuid") ?: ""
                    val ats = info.getString("ats") ?: ""
                    val cardCategory = info.getInt("cardCategory")
                    
                    val cardInfo = HashMap<String, Any>()
                    cardInfo["cardType"] = getCardTypeName(cardType)
                    cardInfo["cardCategory"] = getCardCategoryName(cardCategory)
                    cardInfo["uuid"] = uuid
                    cardInfo["ats"] = ats
                    
                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardDetected on main thread")
                        channel?.invokeMethod("onCardDetected", cardInfo)
                    }
                }
                
                @Throws(RemoteException::class)
                override fun onErrorEx(info: Bundle) {
                    Log.d(TAG, "onErrorEx from Binder thread: $info")
                    val code = info.getInt("code")
                    val message = info.getString("message") ?: "未知错误"
                    
                    val errorInfo = HashMap<String, Any>()
                    errorInfo["code"] = code
                    errorInfo["message"] = message
                    
                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardError on main thread")
                        channel?.invokeMethod("onCardError", errorInfo)
                    }
                }
                
                override fun findICCard(atr: String?) {}
                override fun findRFCard(uuid: String?) {}
                override fun onError(code: Int, message: String?) {}
            }
            
            val cardType = 16 or 1 or 4 or 8
            currentReadCardOptV2.checkCard(cardType, checkCardCallback, 60)
            
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting NFC card check: ${e.message}")
            result.error("CHECK_CARD_ERROR", "检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * 停止检测NFC卡
     */
    fun stopCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "Stopping NFC card check")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to stop NFC check.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }
        try {
            currentReadCardOptV2.cardOff(16)
            currentReadCardOptV2.cancelCheckCard()
            checkCardCallback = null
            resultCallback = null
            
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping NFC card check: ${e.message}")
            result.error("CANCEL_CHECK_CARD_ERROR", "停止检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * M1卡认证
     */
    fun m1Auth(sector: Int, keyType: Int, key: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 auth - sector: $sector, keyType: $keyType, key: $key")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying M1 auth.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }
        try {
            val keyBytes = hexStringToByteArray(key)
            val block = sector * 4
            val code = currentReadCardOptV2.mifareAuth(keyType, block, keyBytes) ?: -1
            
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> AidlErrorCodeV2.valueOf(code).msg
                }
                
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "认证失败: $errorMsg"
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "认证异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 读取M1卡数据块
     */
    fun m1ReadBlock(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 read block - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val hexStr = bytesToHexString(blockData.copyOf(code))
                val response = HashMap<String, Any>()
                response["success"] = true
                val data = HashMap<String, Any>()
                data["block$block"] = hexStr
                response["data"] = data
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "读取失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 read block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "读取异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 写入M1卡数据块
     */
    fun m1WriteBlock(sector: Int, block: Int, data: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 write block - sector: $sector, block: $block, data: $data")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = hexStringToByteArray(data)
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, blockData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "写入失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "写入异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 初始化M1卡钱包
     */
    fun m1InitWallet(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 init wallet - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val initData = getInitWalletData(blockIndex)
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, initData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "初始化钱包失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 init wallet: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "初始化钱包异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取M1卡钱包余额
     */
    fun m1GetBalance(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 get balance - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val balance = getInt32FromBytes(blockData, 0, true)
                val response = HashMap<String, Any>()
                response["success"] = true
                response["balance"] = balance
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "获取余额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 get balance: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "获取余额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 增加M1卡钱包金额
     */
    fun m1IncreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 increase value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            val code = currentReadCardOptV2.mifareIncValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "增加金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 increase value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "增加金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 减少M1卡钱包金额
     */
    fun m1DecreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 decrease value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            val code = currentReadCardOptV2.mifareDecValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "减少金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 decrease value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "减少金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * M1卡恢复操作
     */
    fun m1Restore(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 restore - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }
        try {
            val blockIndex = sector * 4 + block
            val code = currentReadCardOptV2.mifareRestore(blockIndex) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    else -> "恢复操作失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 restore: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "恢复操作异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取卡类型名称
     */
    private fun getCardTypeName(cardType: Int): String {
        return when (cardType) {
            1 -> "MIFARE" // AidlConstantsV2.CardType.MIFARE
            2 -> "MIFARE Ultralight" // AidlConstantsV2.CardType.MIFARE_UL
            4 -> "FeliCa" // AidlConstantsV2.CardType.FELICA
            8 -> "ISO15693" // AidlConstantsV2.CardType.ISO15693
            16 -> "NFC" // AidlConstantsV2.CardType.NFC
            else -> "未知卡类型($cardType)"
        }
    }
    
    /**
     * 获取卡类别名称
     */
    private fun getCardCategoryName(cardCategory: Int): String {
        return when (cardCategory.toChar()) {
            'A' -> "A"
            'B' -> "B"
            else -> "未知类别($cardCategory)"
        }
    }
    
    /**
     * 16进制字符串转字节数组
     */
    private fun hexStringToByteArray(hexString: String): ByteArray {
        val hexStr = hexString.replace(" ", "")
        val len = hexStr.length
        val result = ByteArray(len / 2)
        
        for (i in 0 until len step 2) {
            val high = Character.digit(hexStr[i], 16)
            val low = Character.digit(hexStr[i + 1], 16)
            result[i / 2] = ((high shl 4) or low).toByte()
        }
        
        return result
    }
    
    /**
     * 字节数组转16进制字符串
     */
    private fun bytesToHexString(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        
        return String(hexChars)
    }
    
    /**
     * 获取初始化钱包数据
     */
    private fun getInitWalletData(blockIndex: Int): ByteArray {
        val result = byteArrayOf(
            0x00, 0x00, 0x00, 0x00,
            0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(),
            0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00
        )
        
        result[12] = (blockIndex and 0xFF).toByte()
        result[13] = (blockIndex and 0xFF).inv().toByte()
        result[14] = (blockIndex and 0xFF).toByte()
        result[15] = (blockIndex and 0xFF).inv().toByte()
        
        return result
    }
    
    /**
     * 字节数组转32位整数
     */
    private fun getInt32FromBytes(bytes: ByteArray, offset: Int, littleEndian: Boolean): Int {
        var result = 0
        
        if (littleEndian) {
            result = result or (bytes[offset].toInt() and 0xFF)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 8)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 3].toInt() and 0xFF) shl 24)
        } else {
            result = result or ((bytes[offset].toInt() and 0xFF) shl 24)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 8)
            result = result or (bytes[offset + 3].toInt() and 0xFF)
        }
        
        return result
    }
    
    /**
     * 32位整数转字节数组
     */
    private fun int32ToBytes(value: Int, littleEndian: Boolean): ByteArray {
        val bytes = ByteArray(4)
        
        if (littleEndian) {
            bytes[0] = (value and 0xFF).toByte()
            bytes[1] = ((value ushr 8) and 0xFF).toByte()
            bytes[2] = ((value ushr 16) and 0xFF).toByte()
            bytes[3] = ((value ushr 24) and 0xFF).toByte()
        } else {
            bytes[0] = ((value ushr 24) and 0xFF).toByte()
            bytes[1] = ((value ushr 16) and 0xFF).toByte()
            bytes[2] = ((value ushr 8) and 0xFF).toByte()
            bytes[3] = (value and 0xFF).toByte()
        }
        
        return bytes
    }
} 