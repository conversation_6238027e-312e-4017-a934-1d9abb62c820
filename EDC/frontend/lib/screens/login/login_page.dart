import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../auth/auth_service.dart';
import '../../services/new_auth_service.dart';
import '../../services/api/api_client.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/shift_service.dart';
import '../../services/new_auth_service.dart';
import '../../widgets/address_configuration_dialog.dart';
import '../../services/device_id_service.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _employeeNoController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _employeeNoFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _updateServerConfiguration();

    // 添加焦点监听器
    _employeeNoFocusNode.addListener(_handleFocusChange);
    _passwordFocusNode.addListener(_handleFocusChange);

    // 设置默认员工编号和密码，方便调试
    _employeeNoController.text = '';
    _passwordController.text = '';

    // 页面加载完成后自动聚焦员工编号输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _employeeNoFocusNode.requestFocus();
      }
    });
  }

  /// 更新服务器配置
  /// 确保认证服务使用最新的服务器配置
  Future<void> _updateServerConfiguration() async {
    try {
      final NewAuthService newAuthService = ref.read(newAuthServiceProvider);
      await newAuthService.updateServerConfiguration();
    } catch (e) {
      // 配置更新失败不应该阻止登录页面显示
      debugPrint('更新服务器配置失败: $e');
    }
  }

  // 处理焦点变化，确保获得焦点时键盘弹出
  void _handleFocusChange() {
    // 检查当前拥有焦点的节点
    if (_employeeNoFocusNode.hasFocus || _passwordFocusNode.hasFocus) {
      // 稍微延迟后显示键盘，确保焦点已经生效
      Future.delayed(const Duration(milliseconds: 100), () {
        SystemChannels.textInput.invokeMethod('TextInput.show');
      });
    }
  }

  // 检查用户是否已登录，如果已登录则直接进入主页
  Future<void> _checkLoginStatus() async {
    // 使用认证服务
    final AuthService authService = ref.read(authServiceProvider);

    // 等待认证服务初始化完成
    if (!authService.initialized) {
      // 监听初始化完成
      authService.addListener(() {
        if (authService.initialized && authService.isLoggedInSync && mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) context.go('/');
          });
        }
      });
    } else if (authService.isLoggedInSync && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) context.go('/');
      });
    }
  }

  // Show address configuration dialog
  Future<void> _showAddressConfigurationDialog() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const AddressConfigurationDialog(),
        fullscreenDialog: true,
      ),
    );
    
    if (result == true) {
      // Configuration updated, can do some processing here
      debugPrint('Address configuration updated');
    }
  }

  @override
  void dispose() {
    // 移除监听器
    _employeeNoFocusNode.removeListener(_handleFocusChange);
    _passwordFocusNode.removeListener(_handleFocusChange);

    _employeeNoController.dispose();
    _passwordController.dispose();
    _employeeNoFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    // 关闭键盘
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final String employeeNo = _employeeNoController.text;
    final String password = _passwordController.text;

    // 使用认证服务
    final AuthService authService = ref.read(authServiceProvider);

    try {
      final bool success = await authService.login(employeeNo, password);
      debugPrint('✅ 新认证API登录成功');

      if (mounted && success) {
        // 停止加载状态
        setState(() {
          _isLoading = false;
        });

        // 🔧 登录成功后初始化ShiftService
        try {
          debugPrint('🔍 [Login] 开始初始化ShiftService...');
          final NewAuthService newAuthService = ref.read(newAuthServiceProvider);
          
          // 设置全局认证服务
          ShiftService.setGlobalAuthService(newAuthService);
          
          // 初始化ShiftService
          await ShiftService().initialize();
          debugPrint('✅ [Login] ShiftService初始化完成');
        } catch (e) {
          debugPrint('⚠️ [Login] ShiftService初始化失败: $e');
          // 不阻止登录流程，继续执行
        }

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Login successful, welcome ${authService.currentEmployee?.name ?? employeeNo}'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green,
          ),
        );

        // 跳转到主页
        await Future.delayed(const Duration(milliseconds: 50));
        if (!mounted) return;
        context.go('/', extra: <String, bool>{'fromLogin': true});
      }
    } catch (e) {
      if (mounted) {
        String errorMsg = 'Login failed';
        if (e is ApiException) {
          // Handle specific error codes from API v2
          final errorCode = e.data?['code'];
          switch (errorCode) {
            case 4001:
              errorMsg = 'No station association found. Please contact administrator.';
              break;
            case 4002:
              errorMsg = 'Insufficient permissions for EDC system. Please contact administrator.';
              break;
            case 4003:
              errorMsg = 'System configuration error. Please contact administrator.';
              break;
            case 1001:
              errorMsg = 'Invalid username or password. Please try again.';
              break;
            default:
              errorMsg = e.message.isNotEmpty ? e.message : 'Login failed. Please try again.';
          }
        } else {
          errorMsg = 'Network error. Please check your connection and try again.';
        }

        setState(() {
          _errorMessage = errorMsg;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取 MediaQuery 数据
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    // 获取顶部安全区域高度（状态栏高度）
    final double topPadding = mediaQuery.padding.top;
    // 获取Theme中的文字样式
    final TextTheme textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      // 点击空白处关闭键盘
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        // 避免键盘弹出时挤压界面
        resizeToAvoidBottomInset: true,
        // 设置背景色以覆盖延伸区域
        backgroundColor: Colors.white,
        body: SafeArea(
          // 移除顶部的 SafeArea，因为我们希望内容延伸到状态栏下
          top: false,
          // 保留底部的 SafeArea 来避免与底部导航栏重叠
          bottom: true,
          child: Center(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              clipBehavior: Clip.none,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    // BP Logo
                    const SizedBox(height: 20),
                    Image.asset(
                      'assets/images/bp_akr_logo_receipt.png',
                      width: 260,
                      height: 130,
                      fit: BoxFit.contain,
                    ),
                    const SizedBox(height: 12),

                    // BP-AKR EDC 标题
                    Text(
                      'BP-AKR EDC',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: Colors.green[600],
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // 副标题
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        'Electronic Data Capture Terminal',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                          height: 1.2,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 30),

                    // 登录卡片 - 简化设计
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              // 错误信息显示 - 简化样式
                              if (_errorMessage != null) ...<Widget>[
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(14),
                                  decoration: BoxDecoration(
                                    color: Colors.red[50],
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red[700],
                                      fontSize: 13,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],

                              // Username input field
                              TextFormField(
                                controller: _employeeNoController,
                                focusNode: _employeeNoFocusNode,
                                decoration: InputDecoration(
                                  labelText: 'Employee ID',
                                  labelStyle: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                  hintText: 'Enter your employee ID',
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 16),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        const BorderSide(color: Color(0xFF2BA839), width: 2),
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[50],
                                ),
                                validator: (String? value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter employee ID';
                                  }
                                  if (!RegExp(r'^[a-zA-Z0-9_]+$')
                                      .hasMatch(value)) {
                                    return 'Employee ID can only contain letters, numbers and underscores';
                                  }
                                  return null;
                                },
                                enabled: !_isLoading,
                                keyboardType: TextInputType.text,
                                textInputAction: TextInputAction.next,
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'[a-zA-Z0-9_]')),
                                ],
                                onFieldSubmitted: (_) {
                                  FocusScope.of(context)
                                      .requestFocus(_passwordFocusNode);
                                },
                              ),

                              const SizedBox(height: 16),

                              // Password input field
                              TextFormField(
                                controller: _passwordController,
                                focusNode: _passwordFocusNode,
                                decoration: InputDecoration(
                                  labelText: 'Password',
                                  labelStyle: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                  hintText: 'Enter your password',
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 16),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide:
                                        const BorderSide(color: Color(0xFF2BA839), width: 2),
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[50],
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _obscurePassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: Colors.grey[600],
                                      size: 20,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _obscurePassword = !_obscurePassword;
                                      });
                                    },
                                  ),
                                ),
                                obscureText: _obscurePassword,
                                validator: (String? value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter password';
                                  }
                                  return null;
                                },
                                enabled: !_isLoading,
                                keyboardType: TextInputType.visiblePassword,
                                textInputAction: TextInputAction.done,
                                onFieldSubmitted: (_) => _handleLogin(),
                              ),

                              const SizedBox(height: 20),

                              // 登录按钮 - 简化设计
                              SizedBox(
                                width: double.infinity,
                                height: 48,
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF2BA839),
                                    foregroundColor: Colors.white,
                                    elevation: 0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 14),
                                  ),
                                  onPressed: _isLoading ? null : _handleLogin,
                                  child: _isLoading
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.5,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : const Text(
                                          'Login',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            height: 1.2,
                                          ),
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Server configuration button - 简化设计
                    OutlinedButton.icon(
                      onPressed: _isLoading ? null : _showAddressConfigurationDialog,
                      icon: const Icon(
                        Icons.settings,
                        size: 18,
                        color: Color(0xFF666666),
                      ),
                      label: const Text(
                        'Server Configuration',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF666666),
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey.shade400),
                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 设备ID显示
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Icon(
                            Icons.phone_android,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Device: ${DeviceIdService.instance.shortDeviceId}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}