import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../../models/auth_models.dart';
import '../../models/member_model.dart';
import '../../models/station_model.dart';
import '../../models/dispenser_model.dart';
import '../api/api_service.dart';
import '../../controllers/fcc_status_controller.dart';
import '../device_id_service.dart';

/// 获取ERP系统需要的TransactionDataItem数据
///
/// 在支付页面组装ERP数据，只使用真实可获取的数据
/// 不使用错误的默认值，确保数据的准确性和可靠性
///
/// [transactionData] 交易数据，包含交易的基本信息（燃油交易数据）
/// [currentUser] 当前登录用户信息，用于获取员工和站点信息
/// [cachedMember] 缓存的会员信息，用于获取客户相关数据
/// [fccStatusController] FCC状态控制器，用于获取Nozzle信息
/// 返回一个包含ERP系统需要的所有冗余数据的Map
Future<Map<String, dynamic>> getErpData(
    Map<String, dynamic>? transactionData,
    AuthUser? currentUser,
    Member? cachedMember,
    FccStatusController? fccStatusController) async {
  // 获取员工信息 - 优先从AuthUser获取，fallback到SharedPreferences
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  final String employeeName = currentUser?.fullName ??
      prefs.getString('employee_name') ?? '';
  final String employeeNo = currentUser?.username ??
      prefs.getString('employee_no') ?? '';

  // === 基础交易信息 ===
  // transactionID: 交易ID - 只使用真实的交易ID，不使用默认值
  final String transactionId = transactionData?['id']?.toString() ??
      transactionData?['transactionId']?.toString() ?? '';

  // slipNumber: 订单编号 - 下单前无法获取，留空
  const String slipNumber = ''; // 空字符串，不使用错误的默认值

  // === 站点信息 ===
  // 优先从AuthUser获取站点信息，fallback到交易数据和SharedPreferences
  int stationId = 0;
  String siteId = '';
  String areaSite = '';

  if (currentUser?.stations.isNotEmpty == true) {
    // 从AuthUser获取第一个站点信息
    final UserStationInfo firstStation = currentUser!.stations.first;
    stationId = firstStation.id;  // 使用stationId字段，而不是station.id
    siteId = firstStation.siteCode; // 直接使用siteCode作为siteID

    // 尝试通过StationApi获取完整的站点详细信息
    try {
      final Station? stationDetail = await ApiService().stationApi.getStationById(stationId);
      if (stationDetail != null) {
        // 优先使用StationDetail的siteCode（更准确）
        if (stationDetail.siteCode.isNotEmpty) {
          siteId = stationDetail.siteCode;
        }
        // 使用StationDetail的address
        if (stationDetail.address != null && stationDetail.address!.isNotEmpty) {
          areaSite = stationDetail.address!;
        }
      }

      // 如果StationDetail没有地址，fallback到UserStationInfo的address信息
      if (areaSite.isEmpty) {
        final AddressInfo address = firstStation.address;

        // 组合地址信息
        final List<String> addressParts = <String>[];
        if (address.street.isNotEmpty) addressParts.add(address.street);
        if (address.city.isNotEmpty) addressParts.add(address.city);
        if (address.province.isNotEmpty) addressParts.add(address.province);
        areaSite = addressParts.join(', ');
      }
    } catch (e) {
      // 如果API调用失败，fallback到UserStationInfo的数据
      final AddressInfo address = firstStation.address;

      // 组合地址信息
      final List<String> addressParts = <String>[];
      if (address.street.isNotEmpty) addressParts.add(address.street);
      if (address.city.isNotEmpty) addressParts.add(address.city);
      if (address.province.isNotEmpty) addressParts.add(address.province);
      areaSite = addressParts.join(', ');
    }
  } else {
    // fallback到交易数据和SharedPreferences
    stationId = (transactionData?['stationId'] as int?) ??
        prefs.getInt('station_id') ?? 0;
    siteId = prefs.getString('site_code') ?? '';
    areaSite = prefs.getString('station_address') ?? '';
  }

  // === 设备信息 ===
  // dispenserNumber: 分液器编号 - 优先从Nozzle获取，fallback到transactionData
  final String pumpId = transactionData?['pumpId']?.toString() ?? '';
  final String nozzleId = transactionData?['nozzleId']?.toString() ?? '';

  String dispenserNumber = '';
  String nozzleNumber = '';

  // 尝试从Nozzle获取dispenserNumber
  if (nozzleId.isNotEmpty && fccStatusController != null) {
    try {
      final Nozzle? nozzle = fccStatusController.getNozzleById(nozzleId);
      if (nozzle != null) {
        dispenserNumber = nozzle.dispenserId;
        nozzleNumber = nozzle.name;
      }
    } catch (e) {
      // 如果获取失败，使用fallback
    }
  }



  // === 设备序列号 ===
  // deviceID: 当前设备的序列号 - 从设备ID服务获取
  final String deviceId = DeviceIdService.instance.deviceId ?? 'UNKNOWN_DEVICE';

  // === 燃油信息 ===
  // productID: 油品代码 - 从燃油交易数据获取
  final String fuelType = transactionData?['fuelType']?.toString() ?? '';
  // fuelGrade用于产品分组
  final String fuelGrade = transactionData?['fuelGrade']?.toString() ?? '';

  // === 金额和体积信息 ===
  // amount: 订单金额 - 从燃油交易数据获取
  final double amount = (transactionData?['totalAmount'] as num?)?.toDouble() ??
      (transactionData?['amount'] as num?)?.toDouble() ??
      0.0;

  // price: 交易单价 - 从燃油交易数据获取
  final double unitPrice = (transactionData?['unitPrice'] as num?)?.toDouble() ??
      (transactionData?['price'] as num?)?.toDouble() ??
      0.0;

  // volume: 交易体积 - 从燃油交易数据获取
  final double volume = (transactionData?['volume'] as num?)?.toDouble() ??
      0.0;

  // === 时间信息 ===
  // transactionDate: 交易时间 - 从PaymentTransactionData的createdAt获取
  final String transactionDate = transactionData?['createdAt']?.toString() ??
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

  // === 车辆信息 ===
  // vehicleType: 车辆类型 - 优先从Member的metadata获取，PaymentTransactionData中没有vehicleType字段
  String? vehicleTypeStr = cachedMember?.metadata['vehicle']?.toString();

  // 车辆类型映射：Car=1, Motorcycle=2, Truck=3, Bus=4, Van=5, SUV=6
  // 1 Dinas, 2 Truck, 3 Car, 4 Motor
  int vehicleType = 0; // 默认为0，表示未知
  String vehicleTypeGroup = '';
  if (vehicleTypeStr != null && vehicleTypeStr.isNotEmpty) {
    switch (vehicleTypeStr.toLowerCase()) {
      case 'car':
        vehicleType = 3;
        vehicleTypeGroup = 'Car';
        break;
      case 'motorcycle':
      case 'motorbike':
      case 'motor':
        vehicleType = 4;
        vehicleTypeGroup = 'Motor';
        break;
      case 'truck':
        vehicleType = 2;
        vehicleTypeGroup = 'Car';
        break;
    }
  }

  // vehicleID: 车牌号 - 优先从Member获取，fallback到transactionData
  String vehicleId = '';
  if (cachedMember?.plateNumbers.isNotEmpty == true) {
    vehicleId = cachedMember!.plateNumbers.first;
  } else {
    vehicleId = transactionData?['vehicleId']?.toString() ?? '';
  }

  // === 计数器信息 ===
  // totalizerStart/End: 起始/结束计数器 - 从燃油交易数据获取
  final double totalizerStart = (transactionData?['totalizerStart'] as num?)?.toDouble() ?? 0.0;
  final double totalizerEnd = (transactionData?['totalizerEnd'] as num?)?.toDouble() ?? 0.0;

  // === 交易时长 ===
  // transactionLength: 交易服务时长 - 从燃油交易数据计算
  int transactionLength = 0;
  if (transactionData?['createdAt'] != null) {
    try {
      final DateTime createdAt = DateTime.parse(transactionData!['createdAt'].toString());
      final DateTime now = DateTime.now();
      transactionLength = now.difference(createdAt).inSeconds;
    } catch (e) {
      // 解析失败时使用0
      transactionLength = 0;
    }
  }

  // === 支付方式信息 ===
  // field_Tambahan_1: 支付方式名称 - 从PaymentTransactionData获取
  final String paymentMethodName = transactionData?['paymentMethodName']?.toString() ??
      transactionData?['paymentMethod']?.toString() ?? '';

  // === 员工信息 ===
  // field_Tambahan_2: 员工名称 - 从SharedPreferences获取
  // operatorID: 员工编号 - 从SharedPreferences获取
  final String operatorId = employeeName;

  // === 优惠信息 ===
  // 从transactionData获取优惠信息
  final String promotionType = transactionData?['promotionType']?.toString() ?? '';
  final double percentDiscount = (transactionData?['percentDiscount'] as num?)?.toDouble() ?? 0.0;
  final double amountPercentDiscount = (transactionData?['amountPercentDiscount'] as num?)?.toDouble() ?? 0.0;
  final double amountDiscount = (transactionData?['amountDiscount'] as num?)?.toDouble() ?? 0.0;
  final int flagItemPromotion = (transactionData?['flagItemPromotion'] as int?) ?? 0;
  final double finalDiscount = (transactionData?['finalDiscount'] as num?)?.toDouble() ?? 0.0;

  // === 会员信息 ===
  // 优先从Member获取会员信息，PaymentTransactionData中没有直接的客户字段
  // 客户信息在 transactionData['memberInfo'] 中
  final String customerName = cachedMember?.name ?? '';
  final String customerPhoneNo = cachedMember?.phone ?? '';
  final String email = cachedMember?.email ?? '';

  // 性别信息 - 从Member的metadata获取
  final String gender = cachedMember?.metadata['gender']?.toString() ?? '';

  // 会员生日 - 从Member获取
  final String? dob = cachedMember?.birthDate?.toIso8601String();

  // 会员ID - 从Member获取
  final String dexRowId = cachedMember?.id ?? '';

  // 会员注册时间 - 从Member获取
  final String dexRowTs = cachedMember?.registrationDate.toIso8601String() ?? '';

  // 构建ERP数据 - 只包含必要字段，没有数据就是空值
  return <String, dynamic>{
    'deviceID': deviceId, // 设备序列号，确保唯一性
    'siteID': siteId, // 站点代码，没有就是空字符串
    'area_Site': areaSite, // 站点地址，没有就是空字符串
    'dispenserNumber': dispenserNumber, // 分液器编号，没有就是空字符串
    'nozzleNumber': nozzleNumber, // 油枪编号，没有就是0
    'vehicleType': vehicleType, // 车辆类型，没有就是0
    'reprint': 0, // 重印标志，固定为0
    'field_Tambahan_1': paymentMethodName, // 支付方式，没有就是空字符串
    'iD_ProductGroup': fuelGrade, // 产品组，没有就是空字符串
    'transactionDate': transactionDate, // 交易时间
    'operatorID': employeeName, // 登录账号的员工名称，没有就是空字符串
  };
}


