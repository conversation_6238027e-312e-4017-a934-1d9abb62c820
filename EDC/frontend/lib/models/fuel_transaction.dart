import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class FuelTransaction extends Equatable {
  const FuelTransaction({
    required this.id,
    required this.transactionNumber,
    required this.stationId,
    required this.pumpId,
    required this.nozzleId,
    required this.fuelType,
    required this.fuelGrade,
    required this.unitPrice,
    required this.volume,
    required this.amount,
    required this.status,
    this.memberCardId,
    this.memberId,
    this.employeeId,
    this.fccTransactionId,
    this.posTerminalId,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.processedAt,
    this.cancelledAt,
  });

  factory FuelTransaction.fromJson(Map<String, dynamic> json) {
    final double rawUnitPrice = (json['unit_price'] as num).toDouble();
    final double rawAmount = (json['amount'] as num).toDouble();

    return FuelTransaction(
      id: json['id'] as String,
      transactionNumber: json['transaction_number'] as String,
      stationId: json['station_id'] as int,
      pumpId: json['pump_id'] as String,
      nozzleId: json['nozzle_id'] as String,
      fuelType: json['fuel_type'] as String,           // 油品 ID, 1;2;3
      fuelGrade: json['fuel_grade']?.toString() ?? '', // 油品名称，如 BP_92; BP Ultimate; BP Ultimate Diesel
      unitPrice: rawUnitPrice,
      volume: (json['volume'] as num).toDouble(),
      amount: rawAmount,
      status: json['status'] as String,
      memberCardId: json['member_card_id'] as String?,
      memberId: _safeParseInt(json['member_id']),
      employeeId: _safeParseInt(json['user_id'] ?? json['employee_id']),  // 优先使用 user_id
      fccTransactionId: json['fcc_transaction_id'] as String?,
      posTerminalId: json['pos_terminal_id'] as String?,
      metadata:
          (json['metadata'] as Map<String, dynamic>?) ?? <String, dynamic>{},
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      processedAt: json['processed_at'] != null
          ? DateTime.parse(json['processed_at'] as String)
          : null,
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
    );
  }

  /// 安全地解析整数字段
  /// 支持 String 和 int 类型的输入
  static int? _safeParseInt(dynamic value) {
    if (value == null) return null;
    
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      if (value.isEmpty || value == 'null') return null;
      return int.tryParse(value);
    }
    
    // 尝试转换其他类型
    try {
      if (value is num) {
        return value.toInt();
      }
      // 尝试转换为字符串再解析
      final String stringValue = value.toString();
      if (stringValue.isEmpty || stringValue == 'null') return null;
      return int.tryParse(stringValue);
    } catch (e) {
      // 转换失败，返回 null
      return null;
    }
  }
  final String id;
  final String transactionNumber;
  final int stationId;
  final String pumpId;
  final String nozzleId;
  final String fuelType;
  final String fuelGrade;
  final double unitPrice;
  final double volume;
  final double amount;
  final String status;
  final String? memberCardId;
  final int? memberId;
  final int? employeeId;
  final String? fccTransactionId;
  final String? posTerminalId;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? processedAt;
  final DateTime? cancelledAt;

  @override
  List<Object?> get props => <Object?>[
        id,
        transactionNumber,
        stationId,
        pumpId,
        nozzleId,
        fuelType,
        fuelGrade,
        unitPrice,
        volume,
        amount,
        status,
        memberCardId,
        memberId,
        employeeId,
        fccTransactionId,
        posTerminalId,
        metadata,
        createdAt,
        updatedAt,
        processedAt,
        cancelledAt,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'transaction_number': transactionNumber,
      'station_id': stationId,
      'pump_id': pumpId,
      'nozzle_id': nozzleId,
      'fuel_type': fuelType,
      'fuel_grade': fuelGrade,
      'unit_price': unitPrice,
      'volume': volume,
      'amount': amount,
      'status': status,
      'member_card_id': memberCardId,
      'member_id': memberId,
              'user_id': employeeId,  // 使用 user_id 字段名
      'fcc_transaction_id': fccTransactionId,
      'pos_terminal_id': posTerminalId,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
    };
  }
}
// 分页响应模型
class FuelTransactionResponse {
  const FuelTransactionResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPage,
  });

  factory FuelTransactionResponse.fromJson(Map<String, dynamic> json) {
    // debugPrint('正在解析燃油交易响应数据: ${json.keys.toList()}');

    // 获取items数组
    final List? itemsList = json['items'] as List<dynamic>?;
    if (itemsList == null) {
      debugPrint('警告: 响应中没有items字段');
      return const FuelTransactionResponse(
        items: <FuelTransaction>[],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPage: 0,
      );
    }

    // 解析items数组
    final List<FuelTransaction> items = <FuelTransaction>[];
    for (int i = 0; i < itemsList.length; i++) {
      try {
        final Map<String, dynamic> item = itemsList[i] as Map<String, dynamic>;
        items.add(FuelTransaction.fromJson(item));
      } catch (e) {
        debugPrint('警告: 解析第${i + 1}个交易记录失败: $e');
        debugPrint('问题数据: ${itemsList[i]}');
        // 继续处理其他记录，不因为单个记录错误而失败
      }
    }

    debugPrint('成功解析${items.length}/${itemsList.length}条交易记录');

    return FuelTransactionResponse(
      items: items,
      total: (json['total'] as int?) ?? items.length,
      page: (json['page'] as int?) ?? 1,
      pageSize: (json['page_size'] as int?) ?? items.length,
      totalPage: (json['total_page'] as int?) ?? 1,
    );
  }
  final List<FuelTransaction> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;
}

// Full Fuel Transaction 模型 - 对应 fuel-transactions-full API
class FullFuelTransaction extends Equatable {
  const FullFuelTransaction({
    required this.id,
    required this.transactionNumber,
    required this.stationId,
    required this.stationName,
    required this.siteCode,
    required this.transactionDateTime,
    this.dispenserNo,
    this.pumpNo,
    this.nozzleNo,
    this.globalNozzleProduct,
    this.attendantName,
    this.shiftId,
    this.shiftName,
    this.customerName,
    this.customerPhone,
    this.vehicleType,
    this.licensePlate,
    this.promotionName,
    required this.unitPrice,
    required this.volume,
    required this.amount,
    required this.discountFuel,
    required this.netAmount,
    this.orderSerialNo,
    this.methodOfPayment,
    this.paymentTime,
    this.fccTransactionId,
    this.posTerminalId,
    this.nozzleStartFillingTime,
    this.nozzleHangupTime,
    this.startTotalizerCount,
    this.endTotalizerCount,
    this.discrepancy,
    required this.status,
    required this.totalizerContinuityStatus,
    this.memberCardId,
    this.memberId,
    this.employeeId,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.processedAt,
    this.cancelledAt,
    this.customNote,
    required this.freeLiter,
    required this.freeLiterAmount,
  });

  factory FullFuelTransaction.fromJson(Map<String, dynamic> json) {
    return FullFuelTransaction(
      id: json['id'].toString(),
      transactionNumber: json['transaction_number'] as String,
      stationId: json['station_id'] as int,
      stationName: json['station_name'] as String,
      siteCode: json['site_code'] as String,
      transactionDateTime: DateTime.parse(json['transaction_date_time'] as String),
      dispenserNo: json['dispenser_no'] as int?,
      pumpNo: json['pump_no'] as int?,
      nozzleNo: json['nozzle_no'] as int?,
      globalNozzleProduct: json['global_nozzle_product'] as String?,
      attendantName: json['attendant_name'] as String?,
      shiftId: json['shift_id'] as int?,
      shiftName: json['shift_name'] as String?,
      customerName: json['customer_name'] as String?,
      customerPhone: json['customer_phone'] as String?,
      vehicleType: json['vehicle_type'] as String?,
      licensePlate: json['license_plate'] as String?,
      promotionName: json['promotion_name'] as String?,
      unitPrice: (json['unit_price'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
      discountFuel: (json['discount_fuel'] as num).toDouble(),
      netAmount: (json['net_amount'] as num).toDouble(),
      orderSerialNo: json['order_serial_no'] as String?,
      methodOfPayment: json['method_of_payment'] as String?,
      paymentTime: json['payment_time'] != null ? DateTime.parse(json['payment_time'] as String) : null,
      fccTransactionId: json['fcc_transaction_id'] as String?,
      posTerminalId: json['pos_terminal_id'] as String?,
      nozzleStartFillingTime: json['nozzle_start_filling_time'] != null ? DateTime.parse(json['nozzle_start_filling_time'] as String) : null,
      nozzleHangupTime: json['nozzle_hangup_time'] != null ? DateTime.parse(json['nozzle_hangup_time'] as String) : null,
      startTotalizerCount: json['start_totalizer_count'] as double?,
      endTotalizerCount: json['end_totalizer_count'] as double?,
      discrepancy: json['discrepancy'] as double?,
      status: json['status'] as String,
      totalizerContinuityStatus: json['totalizer_continuity_status'] as String,
      memberCardId: json['member_card_id'] as String?,
      memberId: json['member_id'] as int?,
      employeeId: json['employee_id'] as int?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      processedAt: json['processed_at'] != null ? DateTime.parse(json['processed_at'] as String) : null,
      cancelledAt: json['cancelled_at'] != null ? DateTime.parse(json['cancelled_at'] as String) : null,
      customNote: json['custom_note'] as String?,
      freeLiter: (json['free_liter'] as num).toDouble(),
      freeLiterAmount: (json['free_liter_amount'] as num).toDouble(),
    );
  }

  final String id;
  final String transactionNumber;
  final int stationId;
  final String stationName;
  final String siteCode;
  final DateTime transactionDateTime;
  final int? dispenserNo;
  final int? pumpNo;
  final int? nozzleNo;
  final String? globalNozzleProduct;
  final String? attendantName;
  final int? shiftId;
  final String? shiftName;
  final String? customerName;
  final String? customerPhone;
  final String? vehicleType;
  final String? licensePlate;
  final String? promotionName;
  final double unitPrice;
  final double volume;
  final double amount;
  final double discountFuel;
  final double netAmount;
  final String? orderSerialNo;
  final String? methodOfPayment;
  final DateTime? paymentTime;
  final String? fccTransactionId;
  final String? posTerminalId;
  final DateTime? nozzleStartFillingTime;
  final DateTime? nozzleHangupTime;
  final double? startTotalizerCount;
  final double? endTotalizerCount;
  final double? discrepancy;
  final String status;
  final String totalizerContinuityStatus;
  final String? memberCardId;
  final int? memberId;
  final int? employeeId;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? processedAt;
  final DateTime? cancelledAt;
  final String? customNote;
  final double freeLiter;
  final double freeLiterAmount;

  @override
  List<Object?> get props => <Object?>[
        id,
        transactionNumber,
        stationId,
        stationName,
        siteCode,
        transactionDateTime,
        dispenserNo,
        pumpNo,
        nozzleNo,
        globalNozzleProduct,
        attendantName,
        shiftId,
        shiftName,
        customerName,
        customerPhone,
        vehicleType,
        licensePlate,
        promotionName,
        unitPrice,
        volume,
        amount,
        discountFuel,
        netAmount,
        orderSerialNo,
        methodOfPayment,
        paymentTime,
        fccTransactionId,
        posTerminalId,
        nozzleStartFillingTime,
        nozzleHangupTime,
        startTotalizerCount,
        endTotalizerCount,
        discrepancy,
        status,
        totalizerContinuityStatus,
        memberCardId,
        memberId,
        employeeId,
        metadata,
        createdAt,
        updatedAt,
        processedAt,
        cancelledAt,
        customNote,
        freeLiter,
        freeLiterAmount,
      ];
}

// Full Fuel Transaction 响应模型
class FullFuelTransactionResponse {
  const FullFuelTransactionResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPage,
  });

  factory FullFuelTransactionResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic>? itemsList = json['items'] as List<dynamic>?;
    if (itemsList == null) {
      return const FullFuelTransactionResponse(
        items: <FullFuelTransaction>[],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPage: 0,
      );
    }

    final List<FullFuelTransaction> items = <FullFuelTransaction>[];
    for (int i = 0; i < itemsList.length; i++) {
      try {
        final Map<String, dynamic> item = itemsList[i] as Map<String, dynamic>;
        items.add(FullFuelTransaction.fromJson(item));
      } catch (e) {
        debugPrint('警告: 解析第${i + 1}个交易记录失败: $e');
        // 继续处理其他记录
      }
    }

    return FullFuelTransactionResponse(
      items: items,
      total: (json['total'] as int?) ?? items.length,
      page: (json['page'] as int?) ?? 1,
      pageSize: (json['page_size'] as int?) ?? items.length,
      totalPage: (json['total_page'] as int?) ?? 1,
    );
  }

  final List<FullFuelTransaction> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;

  bool get hasMore => page < totalPage;
}

// Full Fuel Transaction 查询参数
class FullFuelTransactionQueryParams {
  const FullFuelTransactionQueryParams({
    this.page = 1,
    this.limit = 10,
    this.sortBy = 'created_at',
    this.sortDir = 'desc',
    this.stationId,
    this.status,
    this.dateFrom,
    this.dateTo,
  });

  final int page;
  final int limit;
  final String sortBy;
  final String sortDir;
  final int? stationId;
  final String? status;
  final DateTime? dateFrom;
  final DateTime? dateTo;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_dir': sortDir,
    };

    if (stationId != null) json['station_id'] = stationId;
    if (status != null) json['status'] = status;
    if (dateFrom != null) json['date_from'] = dateFrom!.toIso8601String();
    if (dateTo != null) json['date_to'] = dateTo!.toIso8601String();

    return json;
  }

  FullFuelTransactionQueryParams copyWith({
    int? page,
    int? limit,
    String? sortBy,
    String? sortDir,
    int? stationId,
    String? status,
    DateTime? dateFrom,
    DateTime? dateTo,
  }) {
    return FullFuelTransactionQueryParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortDir: sortDir ?? this.sortDir,
      stationId: stationId ?? this.stationId,
      status: status ?? this.status,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
    );
  }
}

// 查询参数模型
class FuelTransactionQueryParams {
  const FuelTransactionQueryParams({
    this.stationId,
    this.status,
    this.pumpId,
    this.nozzleId, // 新增：支持nozzle_id筛选
    this.memberId,
    this.dateFrom,
    this.dateTo,
    this.transactionNumber,
    this.fuelType,
    this.fuelGrade,
    this.page = 1,
    this.limit = 10,
    this.sortBy = 'created_at',
    this.sortDir = 'desc',
  });
  final int? stationId;
  final String? status;
  final String? pumpId;
  final String? nozzleId; // 新增：nozzle_id参数
  final int? memberId;
  final String? dateFrom;
  final String? dateTo;
  final String? transactionNumber;
  final String? fuelType;
  final String? fuelGrade;
  final int page;
  final int limit;
  final String sortBy;
  final String sortDir;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_dir': sortDir,
    };

    if (stationId != null) params['station_id'] = stationId;
    if (status != null) params['status'] = status;
    if (pumpId != null) params['pump_id'] = pumpId;
    if (nozzleId != null) params['nozzle_id'] = nozzleId; // 新增：nozzle_id参数
    if (memberId != null) params['member_id'] = memberId;
    if (dateFrom != null) params['date_from'] = dateFrom;
    if (dateTo != null) params['date_to'] = dateTo;
    if (transactionNumber != null) {
      params['transaction_number'] = transactionNumber;
    }
    if (fuelType != null) params['fuel_type'] = fuelType;
    if (fuelGrade != null) params['fuel_grade'] = fuelGrade;

    return params;
  }
}
